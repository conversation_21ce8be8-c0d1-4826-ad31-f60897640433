[workspace]
# A separate workspace

[package]
name = "sea-query-diesel"
version = "0.3.0-rc.1"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
edition = "2024"
description = "Binder traits for connecting sea-query with diesel"
license = "MIT OR Apache-2.0"
documentation = "https://docs.rs/sea-query"
repository = "https://github.com/SeaQL/sea-query"
categories = ["database"]
keywords = ["database", "sql", "diesel"]
rust-version = "1.85.0"

[lib]

[dependencies]
sea-query = { version = "1.0.0-rc.1", path = "..", default-features = false }
diesel = { version = "2.1.1", features = [
  "i-implement-a-third-party-backend-and-opt-into-breaking-changes",
] }
bigdecimal = { version = "0.4", default-features = false, optional = true }
rust_decimal = { version = "1", default-features = false, optional = true }
chrono = { version = "0.4", default-features = false, optional = true }
time = { version = "0.3.36", default-features = false, optional = true }
uuid = { version = "1", default-features = false, optional = true }
serde_json = { version = "1", default-features = false, optional = true }
ipnetwork = { version = "0.20", default-features = false, optional = true }
mac_address = { version = "1.1", default-features = false, optional = true }
pgvector = { version = "~0.4", default-features = false, optional = true }

[features]
default = []
extras = [
  "with-chrono",
  "with-json",
  "with-rust_decimal",
  "with-bigdecimal",
  "with-uuid",
  "with-time",
  "with-ipnetwork",
  "with-mac_address",
  "postgres-array",
]
postgres = ["diesel/postgres", "sea-query/backend-postgres"]
mysql = ["diesel/mysql", "sea-query/backend-mysql"]
sqlite = ["diesel/sqlite", "sea-query/backend-sqlite"]
with-chrono = ["diesel/chrono", "sea-query/with-chrono", "chrono"]
with-json = ["diesel/serde_json", "sea-query/with-json", "serde_json"]
with-rust_decimal = ["sea-query/with-rust_decimal", "rust_decimal"]
with-rust_decimal-mysql = ["with-rust_decimal", "rust_decimal/db-diesel2-mysql"]
with-rust_decimal-postgres = [
  "with-rust_decimal",
  "rust_decimal/db-diesel2-postgres",
]
with-bigdecimal = ["diesel/numeric", "sea-query/with-bigdecimal", "bigdecimal"]
with-uuid = ["diesel/uuid", "sea-query/with-uuid", "uuid"]
with-time = ["diesel/time", "sea-query/with-time", "time"]
with-ipnetwork = [
  "diesel/network-address",
  "sea-query/with-ipnetwork",
  "ipnetwork",
]
with-mac_address = ["sea-query/with-mac_address", "mac_address"]
postgres-array = ["sea-query/postgres-array"]
postgres-vector = ["sea-query/postgres-vector", "pgvector/diesel"]
