[workspace]
# A separate workspace

[package]
name = "sea-query-rusqlite-example"
version = "0.1.0"
edition = "2024"
rust-version = "1.85.0"
publish = false

[dependencies]
chrono = { version = "0.4", default-features = false, features = ["clock"] }
time = { version = "0.3.36", features = ["parsing", "macros"] }
serde_json = { version = "1" }
uuid = { version = "1", features = ["serde", "v4"] }
rusqlite = { version = "0.37" }
sea-query = { path = "../.." }
sea-query-rusqlite = { path = "../../sea-query-rusqlite", features = [
    "with-chrono",
    "with-json",
    "with-uuid",
    "with-time",
] }

# NOTE: if you are copying this example into your own project, use the following line instead:
# sea-query = { version = "1.0.0-rc.1" }
# sea-query-rusqlite = { version = "0", features = [...] }
