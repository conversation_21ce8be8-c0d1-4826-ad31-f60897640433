[workspace]
# A separate workspace

[package]
name = "sea-query-postgres-example"
version = "0.1.0"
edition = "2024"
rust-version = "1.85.0"
publish = false

[dependencies]
chrono = { version = "0.4", default-features = false, features = ["clock"] }
time = { version = "0.3.36", features = ["macros"] }
uuid = { version = "1", features = ["serde", "v4"] }
serde_json = "1"
rust_decimal = { version = "1" }
postgres = "0.19"
sea-query = { path = "../../" }
sea-query-postgres = { path = "../../sea-query-postgres", features = [
    "with-uuid",
    "with-chrono",
    "with-json",
    "with-time",
    "postgres-array",
    "with-rust_decimal"
] }

# NOTE: if you are copying this example into your own project, use the following line instead:
# sea-query = { version = "1.0.0-rc.1" }
# sea-query-postgres = { version = "0", features = [...] }
