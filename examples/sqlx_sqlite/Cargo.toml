[workspace]
# A separate workspace

[package]
name = "sea-query-sqlx-sqlite-example"
version = "0.1.0"
edition = "2024"
rust-version = "1.85.0"
publish = false

[dependencies]
chrono = { version = "0.4", default-features = false, features = ["clock"] }
time = { version = "0.3.36", features = ["macros"] }
uuid = { version = "1", features = ["serde", "v4"] }
serde_json = "1"
async-std = { version = "1.8", features = ["attributes"] }
sqlx = "0.8"
sea-query = { path = "../../", features = ["sqlx-utils"]}
sea-query-sqlx = { path = "../../sea-query-sqlx", features = [
    "sqlx-sqlite",
    "with-chrono",
    "with-json",
    "with-uuid",
    "with-time",
    "runtime-async-std-native-tls",
] }

# NOTE: if you are copying this example into your own project, use the following line instead:
# sea-query = { version = "1.0.0-rc.1" }
# sea-query-sqlx = { version = "0", features = [...] }
