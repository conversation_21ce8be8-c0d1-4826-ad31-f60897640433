[workspace]
# A separate workspace

[package]
name = "sea-query-postgres"
version = "0.6.0-rc.1"
authors = [ "<PERSON> <<EMAIL>>" ]
edition = "2024"
description = "Binder traits for connecting sea-query with postgres driver"
license = "MIT OR Apache-2.0"
documentation = "https://docs.rs/sea-query"
repository = "https://github.com/SeaQL/sea-query"
categories = [ "database" ]
keywords = [ "database", "sql", "postgres" ]
rust-version = "1.85.0"

[lib]

[dependencies]
sea-query = { version = "1.0.0-rc.1", path = "..", default-features = false }
postgres-types = { version = "0.2", default-features = false }
pgvector = { version = "~0.4", default-features = false, optional = true }
bytes = { version = "1", default-features = false }
rust_decimal = { version = "1", default-features = false, optional = true }
bigdecimal = { version = "0.4", default-features = false, optional = true }
ipnetwork = { version = "0.20", default-features = false, optional = true }
mac_address = { version = "1.1", default-features = false, optional = true }
eui48 = { version = "1", default-features = false, optional = true }
cidr = { version = "0.2", default-features = false, optional = true }

[features]
with-chrono = ["postgres-types/with-chrono-0_4", "sea-query/with-chrono"]
with-json = ["postgres-types/with-serde_json-1", "sea-query/with-json"]
with-rust_decimal = ["sea-query/with-rust_decimal", "rust_decimal/db-postgres"]
with-bigdecimal = ["sea-query/with-bigdecimal", "bigdecimal"]
with-uuid = ["postgres-types/with-uuid-1", "sea-query/with-uuid"]
with-time = ["postgres-types/with-time-0_3", "sea-query/with-time"]
postgres-array = ["postgres-types/array-impls", "sea-query/postgres-array"]
postgres-vector = ["sea-query/postgres-vector", "pgvector/postgres"]
with-ipnetwork = ["postgres-types/with-cidr-0_2", "sea-query/with-ipnetwork", "ipnetwork", "cidr"]
with-mac_address = ["postgres-types/with-eui48-1", "sea-query/with-mac_address", "mac_address", "eui48"]
