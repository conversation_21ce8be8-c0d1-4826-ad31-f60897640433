use sea_query::I<PERSON>;
use strum::{<PERSON><PERSON><PERSON><PERSON>, IntoEnumIterator};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, EnumIter)]
enum Something {
    // ...the Table can also be overwritten like this
    #[iden = "something_else"]
    Table,
    Id,
    AssetName,
    UserId,
}

fn main() {
    let expected = ["something_else", "id", "asset_name", "user_id"];
    Something::iter().zip(expected).for_each(|(var, exp)| {
        assert_eq!(var.to_string(), exp);
    })
}
