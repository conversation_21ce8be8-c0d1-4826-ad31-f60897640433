use sea_query::I<PERSON>;
use strum::{<PERSON><PERSON><PERSON><PERSON>, IntoEnumIterator};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, EnumIter)]
enum User {
    Table,
    Id,
    FirstName,
    LastName,
    Email,
}

fn main() {
    let expected = ["user", "id", "first_name", "last_name", "email"];
    User::iter().zip(expected).for_each(|(var, exp)| {
        assert_eq!(var.to_string(), exp);
    });
}
