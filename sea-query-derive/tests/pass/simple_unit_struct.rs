use sea_query::{<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>sq<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Postgres<PERSON>uer<PERSON><PERSON>er, QuotedBuilder};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct SomeType;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
#[iden(rename = "Hel`lo")]
pub struct SomeTypeWithRename;

fn main() {
    assert_eq!(SomeType.to_string(), "some_type");
    assert_eq!(SomeTypeWithRename.to_string(), "Hel`lo");

    let mut string = String::new();
    PostgresQueryBuilder.prepare_iden(&SomeType.into_iden(), &mut string);
    assert_eq!(string, "\"some_type\"");

    let mut string = String::new();
    PostgresQueryBuilder.prepare_iden(&SomeTypeWithRename.into_iden(), &mut string);
    assert_eq!(string, "\"Hel`lo\"");

    let mut string = String::new();
    MysqlQueryBuilder.prepare_iden(&SomeTypeWithRename.into_iden(), &mut string);
    assert_eq!(string, "`Hel``lo`");
}
