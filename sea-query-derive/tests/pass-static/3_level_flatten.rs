use sea_query::{<PERSON><PERSON>, <PERSON>den<PERSON>tatic};
use strum::{<PERSON><PERSON><PERSON><PERSON>, IntoEnumIterator};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>tatic, EnumIter)]
enum Asset {
    Table,
    Id,
    AssetName,
    #[iden(flatten)]
    First {
        first: FirstLevel,
    },
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, IdenStatic)]
enum FirstLevel {
    LevelOne,
    #[iden(flatten)]
    Second(SecondLevel),
}

#[derive(<PERSON><PERSON>, <PERSON>lone, IdenStatic, EnumIter)]
enum SecondLevel {
    LevelTwo,
    #[iden(flatten)]
    Third(LevelThree),
    UserId,
}

#[derive(Co<PERSON>, <PERSON>lone, IdenStatic, Default)]
struct LevelThree;

impl Default for FirstLevel {
    fn default() -> Self {
        Self::LevelOne
    }
}

fn main() {
    // custom ends up being default string which is an empty string
    let expected = [
        "asset",
        "id",
        "asset_name",
        "level_one",
        "level_two",
        "level_three",
        "user_id",
    ];
    Asset::iter()
        .chain(
            SecondLevel::iter()
                .map(FirstLevel::Second)
                .map(|s| Asset::First { first: s }),
        )
        .zip(expected)
        .for_each(|(var, exp)| {
            assert_eq!(var.to_string(), exp);
            assert_eq!(var.as_str(), exp)
        })
}
