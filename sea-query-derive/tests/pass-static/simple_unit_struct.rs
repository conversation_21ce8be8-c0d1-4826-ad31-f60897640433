use sea_query::{<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Mysql<PERSON><PERSON><PERSON><PERSON>er, PostgresQueryBuilder, QuotedBuilder};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>denStatic)]
pub struct SomeType;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>tatic)]
#[iden(rename = "Hel`lo")]
pub struct SomeTypeWithRename;

fn main() {
    assert_eq!(SomeType.to_string(), "some_type");
    assert_eq!(SomeTypeWithRename.to_string(), "Hel`lo");

    let mut string = String::new();
    PostgresQueryBuilder.prepare_iden(&SomeType.into_iden(), &mut string);
    assert_eq!(string, "\"some_type\"");

    let mut string = String::new();
    PostgresQueryBuilder.prepare_iden(&SomeTypeWithRename.into_iden(), &mut string);
    assert_eq!(string, "\"Hel`lo\"");

    let mut string = String::new();
    MysqlQuer<PERSON><PERSON><PERSON><PERSON>.prepare_iden(&SomeTypeWithRename.into_iden(), &mut string);
    assert_eq!(string, "`Hel``lo`");
}
