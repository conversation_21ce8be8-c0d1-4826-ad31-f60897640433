use sea_query::{<PERSON><PERSON>, <PERSON>den<PERSON>tatic};
use strum::{<PERSON>um<PERSON>ter, IntoEnumIterator};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>den<PERSON>tatic, EnumIter)]
enum User {
    Table,
    Id,
    FirstName,
    LastName,
    Email,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>den<PERSON>tatic, EnumIter)]
enum UserStatic {
    Table,
    Id,
    FirstName,
    LastName,
    Email,
}

fn main() {
    let expected = ["user", "id", "first_name", "last_name", "email"];
    User::iter().zip(expected).for_each(|(var, exp)| {
        assert_eq!(var.to_string(), exp);
        assert_eq!(var.as_str(), exp)
    });
}
