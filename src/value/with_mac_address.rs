use super::*;

type_to_value!(<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>);

impl Value {
    pub fn is_mac_address(&self) -> bool {
        matches!(self, Self::<PERSON><PERSON><PERSON><PERSON>(_))
    }

    pub fn as_ref_mac_address(&self) -> Option<&MacAddress> {
        match self {
            Self::<PERSON><PERSON><PERSON><PERSON>(v) => v.as_ref(),
            _ => panic!("not Value::<PERSON><PERSON><PERSON><PERSON>"),
        }
    }
}
