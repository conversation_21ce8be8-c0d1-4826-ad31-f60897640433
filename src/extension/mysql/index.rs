use crate::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>};

pub type IndexName = Alias;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct IndexHint {
    pub index: DynIden,
    pub r#type: IndexHintType,
    pub scope: IndexHintScope,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
#[non_exhaustive]
pub enum IndexHintType {
    Use,
    Ignore,
    Force,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
#[non_exhaustive]
pub enum IndexHintScope {
    Join,
    OrderBy,
    GroupBy,
    All,
}
