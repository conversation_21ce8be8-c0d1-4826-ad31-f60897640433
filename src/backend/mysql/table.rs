use super::*;

impl TableBuilder for MysqlQueryBuilder {
    fn prepare_table_opt(&self, create: &TableCreateStatement, sql: &mut dyn SqlWriter) {
        // comment
        if let Some(comment) = &create.comment {
            let comment = self.escape_string(comment);
            write!(sql, " COMMENT '{comment}'").unwrap();
        }
        self.prepare_table_opt_def(create, sql)
    }

    fn prepare_column_def(&self, column_def: &ColumnDef, sql: &mut dyn SqlWriter) {
        self.prepare_iden(&column_def.name, sql);

        if let Some(column_type) = &column_def.types {
            write!(sql, " ").unwrap();
            self.prepare_column_type(column_type, sql);
        }

        for column_spec in column_def.spec.iter() {
            write!(sql, " ").unwrap();
            self.prepare_column_spec(column_spec, sql);
        }
    }

    fn prepare_column_type(&self, column_type: &ColumnType, sql: &mut dyn SqlWriter) {
        match column_type {
            ColumnType::Char(length) => match length {
                Some(length) => write!(sql, "char({length})"),
                None => write!(sql, "char"),
            },
            ColumnType::String(length) => match length {
                StringLen::N(length) => write!(sql, "varchar({length})"),
                StringLen::None => write!(sql, "varchar(255)"),
                StringLen::Max => write!(sql, "varchar(65535)"),
            },
            ColumnType::Text => write!(sql, "text"),
            ColumnType::TinyInteger | ColumnType::TinyUnsigned => write!(sql, "tinyint"),
            ColumnType::SmallInteger | ColumnType::SmallUnsigned => write!(sql, "smallint"),
            ColumnType::Integer | ColumnType::Unsigned => write!(sql, "int"),
            ColumnType::BigInteger | ColumnType::BigUnsigned => write!(sql, "bigint"),
            ColumnType::Float => write!(sql, "float"),
            ColumnType::Double => write!(sql, "double"),
            ColumnType::Decimal(precision) => match precision {
                Some((precision, scale)) => write!(sql, "decimal({precision}, {scale})"),
                None => write!(sql, "decimal"),
            },
            ColumnType::DateTime => write!(sql, "datetime"),
            ColumnType::Timestamp => write!(sql, "timestamp"),
            ColumnType::TimestampWithTimeZone => write!(sql, "timestamp"),
            ColumnType::Time => write!(sql, "time"),
            ColumnType::Date => write!(sql, "date"),
            ColumnType::Year => write!(sql, "year"),
            ColumnType::Interval(_, _) => write!(sql, "unsupported"),
            ColumnType::Binary(length) => write!(sql, "binary({length})"),
            ColumnType::VarBinary(length) => match length {
                StringLen::N(length) => write!(sql, "varbinary({length})"),
                StringLen::None => write!(sql, "varbinary(255)"),
                StringLen::Max => write!(sql, "varbinary(65535)"),
            },
            ColumnType::Blob => write!(sql, "blob"),
            ColumnType::Bit(length) => match length {
                Some(length) => write!(sql, "bit({length})"),
                None => write!(sql, "bit"),
            },
            ColumnType::VarBit(length) => write!(sql, "bit({length})"),
            ColumnType::Boolean => write!(sql, "bool"),
            ColumnType::Money(precision) => match precision {
                Some((precision, scale)) => write!(sql, "decimal({precision}, {scale})"),
                None => write!(sql, "decimal"),
            },
            ColumnType::Json => write!(sql, "json"),
            ColumnType::JsonBinary => write!(sql, "json"),
            ColumnType::Uuid => write!(sql, "binary(16)"),
            ColumnType::Custom(iden) => write!(sql, "{iden}"),
            ColumnType::Enum { variants, .. } => {
                write!(sql, "ENUM('").unwrap();
                let mut viter = variants.iter();
                if let Some(variant) = viter.next() {
                    write!(sql, "{variant}",).unwrap();
                }
                for variant in viter {
                    write!(sql, "', '").unwrap();
                    write!(sql, "{variant}",).unwrap();
                }
                write!(sql, "')")
            }
            ColumnType::Array(_) => unimplemented!("Array is not available in MySQL."),
            ColumnType::Vector(_) => unimplemented!("Vector is not available in MySQL."),
            ColumnType::Cidr => unimplemented!("Cidr is not available in MySQL."),
            ColumnType::Inet => unimplemented!("Inet is not available in MySQL."),
            ColumnType::MacAddr => unimplemented!("MacAddr is not available in MySQL."),
            ColumnType::LTree => unimplemented!("LTree is not available in MySQL."),
        }
        .unwrap();

        if matches!(
            column_type,
            ColumnType::TinyUnsigned
                | ColumnType::SmallUnsigned
                | ColumnType::Unsigned
                | ColumnType::BigUnsigned
        ) {
            write!(sql, " ").unwrap();
            write!(sql, "UNSIGNED").unwrap();
        }
    }

    fn column_spec_auto_increment_keyword(&self) -> &str {
        "AUTO_INCREMENT"
    }

    fn prepare_table_alter_statement(&self, alter: &TableAlterStatement, sql: &mut dyn SqlWriter) {
        if alter.options.is_empty() {
            panic!("No alter option found")
        };
        write!(sql, "ALTER TABLE ").unwrap();
        if let Some(table) = &alter.table {
            self.prepare_table_ref_table_stmt(table, sql);
            write!(sql, " ").unwrap();
        }
        alter.options.iter().fold(true, |first, option| {
            if !first {
                write!(sql, ", ").unwrap();
            };
            match option {
                TableAlterOption::AddColumn(AddColumnOption {
                    column,
                    if_not_exists,
                }) => {
                    write!(sql, "ADD COLUMN ").unwrap();
                    if *if_not_exists {
                        write!(sql, "IF NOT EXISTS ").unwrap();
                    }
                    self.prepare_column_def(column, sql);
                }
                TableAlterOption::ModifyColumn(column_def) => {
                    write!(sql, "MODIFY COLUMN ").unwrap();
                    self.prepare_column_def(column_def, sql);
                }
                TableAlterOption::RenameColumn(from_name, to_name) => {
                    write!(sql, "RENAME COLUMN ").unwrap();
                    self.prepare_iden(from_name, sql);
                    write!(sql, " TO ").unwrap();
                    self.prepare_iden(to_name, sql);
                }
                TableAlterOption::DropColumn(column_name) => {
                    write!(sql, "DROP COLUMN ").unwrap();
                    self.prepare_iden(column_name, sql);
                }
                TableAlterOption::DropForeignKey(name) => {
                    let mut foreign_key = TableForeignKey::new();
                    foreign_key.name(name.to_string());
                    let drop = ForeignKeyDropStatement {
                        foreign_key,
                        table: None,
                    };
                    self.prepare_foreign_key_drop_statement_internal(&drop, sql, Mode::TableAlter);
                }
                TableAlterOption::AddForeignKey(foreign_key) => {
                    let create = ForeignKeyCreateStatement {
                        foreign_key: foreign_key.to_owned(),
                    };
                    self.prepare_foreign_key_create_statement_internal(
                        &create,
                        sql,
                        Mode::TableAlter,
                    );
                }
            };
            false
        });
    }

    fn prepare_table_rename_statement(
        &self,
        rename: &TableRenameStatement,
        sql: &mut dyn SqlWriter,
    ) {
        write!(sql, "RENAME TABLE ").unwrap();
        if let Some(from_name) = &rename.from_name {
            self.prepare_table_ref_table_stmt(from_name, sql);
        }
        write!(sql, " TO ").unwrap();
        if let Some(to_name) = &rename.to_name {
            self.prepare_table_ref_table_stmt(to_name, sql);
        }
    }

    /// column comment
    fn column_comment(&self, comment: &str, sql: &mut dyn SqlWriter) {
        let comment = self.escape_string(comment);
        write!(sql, "COMMENT '{comment}'").unwrap()
    }
}
