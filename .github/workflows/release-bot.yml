name: Release Bot

on:
  release:
    types: [published]

jobs:
  comment:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - name: Commenting on `${{ github.event.release.tag_name }}` release
        uses: billy1624/release-comment-on-pr@master
        with:
          release-tag: ${{ github.event.release.tag_name }}
          token: ${{ github.token }}
          message: |
            ### :tada: Released In [${releaseTag}](${releaseUrl}) :tada:
            
            Thank you everyone for the contribution!
            This feature is now available in the latest release. Now is a good time to upgrade!
            Your participation is what makes us unique; your adoption is what drives us forward.
            You can support SeaQL 🌊 by starring our repos, sharing our libraries and becoming a sponsor ⭐.
